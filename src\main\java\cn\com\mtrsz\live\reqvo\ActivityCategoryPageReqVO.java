package cn.com.mtrsz.live.reqvo;

import cn.com.mtrsz.live.common.QueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityCategoryPageReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  活动类别分页查询请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("活动类别分页查询请求参数")
public class ActivityCategoryPageReqVO extends QueryRequest {

    @ApiModelProperty(value = "活动类别名称，支持模糊查询")
    private String categoryName;

    @ApiModelProperty(value = "创建开始时间，格式：yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "创建结束时间，格式：yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
