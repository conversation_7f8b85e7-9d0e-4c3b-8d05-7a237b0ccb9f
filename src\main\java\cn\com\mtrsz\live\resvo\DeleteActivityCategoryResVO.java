package cn.com.mtrsz.live.resvo;

import cn.com.mtrsz.live.common.BasicResponseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright (C), 2010-2023
 * FileName:     DeleteActivityCategoryResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  删除活动类别响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("删除活动类别响应")
public class DeleteActivityCategoryResVO extends BasicResponseVO {

    @ApiModelProperty(value = "删除的活动类别ID")
    private Integer id;

    @ApiModelProperty(value = "删除是否成功")
    private Boolean success;
}
