package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     MyRegistrationDetailResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  个人报名详情响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("个人报名详情响应")
public class MyRegistrationDetailResVO {

    @ApiModelProperty(value = "报名记录ID")
    private Integer id;

    @ApiModelProperty(value = "活动ID")
    private Integer activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动介绍")
    private String activityDesc;

    @ApiModelProperty(value = "活动图片URL，多个图片逗号分割")
    private String activityImageUrl;

    @ApiModelProperty(value = "活动开始时间")
    private Date startTime;

    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;

    @ApiModelProperty(value = "报名数据JSON，存储用户填写的表单数据")
    private String registrationData;

    @ApiModelProperty(value = "报名时间")
    private Date registrationTime;

    @ApiModelProperty(value = "报名状态，枚举值：10(已报名)、20(已取消)")
    private String registrationStatus;
}
