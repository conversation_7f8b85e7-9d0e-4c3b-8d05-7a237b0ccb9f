package cn.com.mtrsz.live.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright (C), 2010-2023
 * FileName:     PageResult
 * Author:       System
 * Date:         2023/12/01
 * Description:  分页结果封装类
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
public class PageResult<T> {
    
    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "数据列表")
    private List<T> list;
    
    @ApiModelProperty(value = "当前页码")
    private Integer pageNum;
    
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;
}
