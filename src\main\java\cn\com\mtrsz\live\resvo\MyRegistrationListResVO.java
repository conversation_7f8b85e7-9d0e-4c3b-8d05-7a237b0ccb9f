package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright (C), 2010-2023
 * FileName:     MyRegistrationListResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  个人报名列表查询响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("个人报名列表查询响应")
public class MyRegistrationListResVO {

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "报名记录列表")
    private List<MyRegistrationItemResVO> list;
}
