package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityCategoryPageResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  活动类别分页查询响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("活动类别分页查询响应")
public class ActivityCategoryPageResVO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;

    @ApiModelProperty(value = "活动类别描述")
    private String categoryDesc;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
}
