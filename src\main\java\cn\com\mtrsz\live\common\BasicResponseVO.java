package cn.com.mtrsz.live.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     BasicResponseVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  基础响应VO类
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
public class BasicResponseVO {
    
    @ApiModelProperty(value = "业务响应码", required = true)
    private String resultCode;

    @ApiModelProperty(value = "业务响应信息", required = true)
    private String resultMsg;
}
