package cn.com.mtrsz.live.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     QueryRequest
 * Author:       System
 * Date:         2023/12/01
 * Description:  分页查询基础请求类
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
public class QueryRequest {
    
    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页大小，默认10")
    private Integer pageSize = 10;
}
