package cn.com.mtrsz.live.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     DeleteActivityCategoryReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  删除活动类别请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("删除活动类别请求参数")
public class DeleteActivityCategoryReqVO {

    @ApiModelProperty(value = "主键ID", required = true)
    private Integer id;
}
