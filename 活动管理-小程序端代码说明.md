# 活动管理系统 - 代码说明

## 已创建的文件列表

### 1. 通用基础类 (common包)
- `QueryRequest.java` - 分页查询基础请求类
- `BasicResponseVO.java` - 基础响应VO类  
- `PageResult.java` - 分页结果封装类
- `Response.java` - 统一响应结果封装类

### 2. Controller层
- `ActivityMiniController.java` - 小程序端活动管理接口控制器
- `ActivityCategoryController.java` - 管理后台活动类别管理接口控制器

### 3. 请求VO类 (reqvo包)
#### 小程序端请求VO
- `ActivityMiniListReqVO.java` - 小程序活动列表查询请求
- `ActivityRegistrationReqVO.java` - 提交报名信息请求
- `MyRegistrationListReqVO.java` - 个人报名列表查询请求

#### 管理后台活动类别请求VO
- `ActivityCategoryPageReqVO.java` - 活动类别分页查询请求
- `AddActivityCategoryReqVO.java` - 新增活动类别请求
- `UpdateActivityCategoryReqVO.java` - 修改活动类别请求
- `DeleteActivityCategoryReqVO.java` - 删除活动类别请求

### 4. 响应VO类 (resvo包)
#### 小程序端响应VO
- `ActivityMiniItemResVO.java` - 小程序活动列表项响应
- `CategoryFormConfigResVO.java` - 活动类别表单配置响应
- `ActivityMiniListResVO.java` - 小程序活动列表查询响应
- `ActivityRegistrationResVO.java` - 提交报名信息响应
- `MyRegistrationItemResVO.java` - 个人报名列表项响应
- `MyRegistrationListResVO.java` - 个人报名列表查询响应
- `MyRegistrationDetailResVO.java` - 个人报名详情响应

#### 管理后台活动类别响应VO
- `ActivityCategoryPageResVO.java` - 活动类别分页查询响应
- `AddActivityCategoryResVO.java` - 新增活动类别响应
- `UpdateActivityCategoryResVO.java` - 修改活动类别响应
- `DeleteActivityCategoryResVO.java` - 删除活动类别响应
- `ActivityCategoryDetailResVO.java` - 活动类别详情响应

## 接口说明

### 小程序端接口

#### 1. 活动列表查询
- **接口路径：** `GET /activityMini/list`
- **功能：** 查询小程序端活动列表，排除未发布和已作废状态的活动
- **特点：** 同时返回活动列表和对应的表单配置

#### 2. 提交报名信息
- **接口路径：** `POST /activityMini/register`
- **功能：** 用户提交活动报名信息
- **验证：** 活动状态、用户唯一性、报名数据格式、人数限制等

#### 3. 个人历史报名数据列表
- **接口路径：** `POST /activityMini/myRegistrations`
- **功能：** 分页查询用户的历史报名记录

#### 4. 个人指定活动详情查询
- **接口路径：** `GET /activityMini/myRegistrationDetail`
- **功能：** 查询用户在指定活动的报名详情

### 管理后台活动类别管理接口

#### 1. 分页查询活动类别
- **接口路径：** `POST /activityCategory/page`
- **功能：** 分页查询活动类别，支持按名称模糊查询和创建时间范围查询

#### 2. 新增活动类别
- **接口路径：** `POST /activityCategory/add`
- **功能：** 新增活动类别，包含表单配置JSON
- **验证：** 类别名称唯一性、表单配置格式验证

#### 3. 修改活动类别
- **接口路径：** `POST /activityCategory/update`
- **功能：** 修改活动类别信息
- **限制：** 如有绑定活动则不允许修改表单配置

#### 4. 删除活动类别
- **接口路径：** `POST /activityCategory/delete`
- **功能：** 删除活动类别
- **限制：** 如有绑定活动则不允许删除

#### 5. 活动类别详情查询
- **接口路径：** `GET /activityCategory/detail`
- **功能：** 查询活动类别详细信息，包含完整表单配置

## 代码特点

1. **严格按照文档规范：** 完全按照活动管理-product.md文档的接口设计要求编写
2. **统一的代码风格：** 参考现有AgreementController的代码风格和注解使用方式
3. **完整的Swagger注解：** 所有接口和字段都添加了详细的Swagger文档注解
4. **参数验证：** Controller中包含基础的参数验证逻辑
5. **TODO标记：** 在需要实现具体业务逻辑的地方添加了TODO注释
6. **继承关系：** 响应VO正确继承BasicResponseVO，请求VO正确继承QueryRequest

## 下一步工作

1. **实现Service层：** 需要创建对应的Service接口和实现类
2. **实现Mapper层：** 需要创建数据库访问层
3. **完善业务逻辑：** 实现Controller中标记为TODO的业务逻辑
4. **添加单元测试：** 为接口编写单元测试用例
5. **集成测试：** 启动项目测试Swagger文档生成是否正确

## 注意事项

- 所有时间字段使用Date类型，前端传参格式为：yyyy-MM-dd HH:mm:ss
- 文件/图片字段支持多个，使用逗号分割的URL字符串
- 活动状态枚举：10(待开始)、20(进行中)、40(已结束)
- 报名状态枚举：10(已报名)、20(已取消)
- 小程序端只显示已发布的活动（排除00-未发布、30-已作废状态）
- 表单配置inputType枚举：input(文本输入框)、file(文件/图片上传)、datetime(时间组件)、date(日期组件)、textarea(多行文本框)
- 活动类别管理需要验证业务约束：有绑定活动时不允许修改表单配置或删除类别
