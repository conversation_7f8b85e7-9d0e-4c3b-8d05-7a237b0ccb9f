package cn.com.mtrsz.live.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     Response
 * Author:       System
 * Date:         2023/12/01
 * Description:  统一响应结果封装类
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
public class Response<T> {
    
    @ApiModelProperty(value = "响应码")
    private String code;
    
    @ApiModelProperty(value = "响应消息")
    private String message;
    
    @ApiModelProperty(value = "响应数据")
    private T data;
    
    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    
    public static <T> Response<T> success(T data) {
        Response<T> response = new Response<>();
        response.setCode("200");
        response.setMessage("操作成功");
        response.setData(data);
        response.setSuccess(true);
        return response;
    }
    
    public static <T> Response<T> success() {
        return success(null);
    }
    
    public static <T> Response<T> error(String message) {
        Response<T> response = new Response<>();
        response.setCode("500");
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }
}
