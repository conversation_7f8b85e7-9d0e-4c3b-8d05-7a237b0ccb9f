package cn.com.mtrsz.live.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     UpdateActivityCategoryReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  修改活动类别请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("修改活动类别请求参数")
public class UpdateActivityCategoryReqVO {

    @ApiModelProperty(value = "主键ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;

    @ApiModelProperty(value = "活动类别描述")
    private String categoryDesc;

    @ApiModelProperty(value = "表单配置JSON，每次修改必须传递完整的JSON配置不支持部分更新。inputType枚举值：input(文本输入框)、file(文件/图片上传支持多个逗号分割MinIO的URL)、datetime(时间组件)、date(日期组件)、textarea(多行文本框)")
    private String formConfig;
}
