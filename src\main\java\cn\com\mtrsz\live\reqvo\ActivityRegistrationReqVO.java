package cn.com.mtrsz.live.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityRegistrationReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  提交报名信息请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("提交报名信息请求参数")
public class ActivityRegistrationReqVO {

    @ApiModelProperty(value = "活动ID", required = true)
    private Integer activityId;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    @ApiModelProperty(value = "报名数据JSON，根据活动类别的表单配置填写，文件/图片字段使用逗号分割的URL字符串", required = true)
    private String registrationData;
}
