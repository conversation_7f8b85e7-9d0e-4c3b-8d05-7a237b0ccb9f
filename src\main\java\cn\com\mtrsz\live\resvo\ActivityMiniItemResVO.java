package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityMiniItemResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  小程序活动列表项响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("小程序活动列表项响应")
public class ActivityMiniItemResVO {

    @ApiModelProperty(value = "活动ID")
    private Integer id;

    @ApiModelProperty(value = "活动类别ID")
    private Integer categoryId;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动介绍")
    private String activityDesc;

    @ApiModelProperty(value = "活动图片URL，多个图片逗号分割")
    private String activityImageUrl;

    @ApiModelProperty(value = "活动开始时间")
    private Date startTime;

    @ApiModelProperty(value = "活动结束时间")
    private Date endTime;

    @ApiModelProperty(value = "活动状态，枚举值：10(待开始)、20(进行中)、40(已结束)")
    private String status;

    @ApiModelProperty(value = "已报名人数，实时计算")
    private Integer registeredCount;

    @ApiModelProperty(value = "最大参与人数限制")
    private Integer maxParticipants;
}
