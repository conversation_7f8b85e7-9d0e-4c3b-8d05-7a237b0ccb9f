package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityMiniListResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  小程序活动列表查询响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("小程序活动列表查询响应")
public class ActivityMiniListResVO {

    @ApiModelProperty(value = "活动列表")
    private List<ActivityMiniItemResVO> activityList;

    @ApiModelProperty(value = "活动类别表单配置")
    private List<CategoryFormConfigResVO> categoryFormConfig;
}
