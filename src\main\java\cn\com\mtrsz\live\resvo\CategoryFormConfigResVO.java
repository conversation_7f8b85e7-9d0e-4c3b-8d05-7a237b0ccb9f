package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     CategoryFormConfigResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  活动类别表单配置响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("活动类别表单配置响应")
public class CategoryFormConfigResVO {

    @ApiModelProperty(value = "活动类别ID")
    private Integer categoryId;

    @ApiModelProperty(value = "表单配置JSON，包含字段中文名、是否必填、输入框类型、输入长度等")
    private String formConfig;
}
