package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityCategoryDetailResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  活动类别详情响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("活动类别详情响应")
public class ActivityCategoryDetailResVO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;

    @ApiModelProperty(value = "活动类别描述")
    private String categoryDesc;

    @ApiModelProperty(value = "表单配置JSON")
    private String formConfig;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}
