package cn.com.mtrsz.live.resvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityRegistrationResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  提交报名信息响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("提交报名信息响应")
public class ActivityRegistrationResVO {

    @ApiModelProperty(value = "报名记录ID")
    private Integer id;

    @ApiModelProperty(value = "活动ID")
    private Integer activityId;

    @ApiModelProperty(value = "报名时间")
    private Date registrationTime;
}
