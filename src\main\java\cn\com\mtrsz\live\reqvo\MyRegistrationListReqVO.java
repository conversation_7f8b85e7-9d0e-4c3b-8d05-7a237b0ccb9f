package cn.com.mtrsz.live.reqvo;

import cn.com.mtrsz.live.common.QueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright (C), 2010-2023
 * FileName:     MyRegistrationListReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  个人报名列表查询请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("个人报名列表查询请求参数")
public class MyRegistrationListReqVO extends QueryRequest {

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;
}
