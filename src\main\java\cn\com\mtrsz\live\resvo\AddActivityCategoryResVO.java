package cn.com.mtrsz.live.resvo;

import cn.com.mtrsz.live.common.BasicResponseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Copyright (C), 2010-2023
 * FileName:     AddActivityCategoryResVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  新增活动类别响应
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("新增活动类别响应")
public class AddActivityCategoryResVO extends BasicResponseVO {

    @ApiModelProperty(value = "新增的活动类别ID")
    private Integer id;

    @ApiModelProperty(value = "活动类别名称")
    private String categoryName;
}
