<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="application/msword; charset=utf-8"/>
    <title>活动管理系统接口设计文档 - 用户报名管理模块</title>
    <style type="text/css">
        .bg {
            font-size: 10.5pt; /* 宋体五号 */
            font-weight: bold;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
            border: 0.5px solid #000;
        }

        tr {
            height: 32px;
            font-size: 10.5pt; /* 宋体五号 */
        }

        td {
            padding-left: 10px;
            border: 0.5px solid #000;
            height: auto;
            min-height: 32px;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 10.5pt; /* 宋体五号 */
            vertical-align: top;
        }

        .bg td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        tr td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        .specialHeight {
            height: 40px;
        }

        .first_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .second_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .doc_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 16pt; /* 文档标题用三号 */
            font-weight: bold;
            text-align: center;
        }

        body {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
        }

        /* 正文段落 */
        div, p {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
        }

        /* 代码示例 */
        pre {
            font-family: "Courier New", monospace;
            font-size: 9pt; /* 代码用小一点的字体 */
            background-color: #f5f5f5;
            padding: 5px;
            margin: 5px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 确保单元格宽度按比例分配 */
        td[width="25%"] {
            width: 25%;
        }
        
        td[width="26%"] {
            width: 26%;
        }
        
        td[width="15%"] {
            width: 15%;
        }
        
        td[width="29%"] {
            width: 29%;
        }
    </style>
</head>

<body>
<div style="width:800px; margin: 0 auto">
    <div>
        <h4 class="doc_title">活动管理系统接口设计文档</h4>
        <h4 class="doc_title">用户报名管理模块及小程序端接口</h4>
        <br>
    </div>
    
    <div style="margin-bottom:20px;">
        <h4 class="first_title">用户报名管理模块</h4>

        <div>
            <h4 class="second_title">分页查询用户报名接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询用户报名列表，支持按活动、用户信息和报名时间筛选。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">分页查询用户报名列表，支持按活动、用户信息和报名时间筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/userRegistration/page</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.activityId</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动ID</td>
                </tr>
                <tr>
                    <td align="left">4.userName</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>用户姓名，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">5.userPhone</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>用户手机号</td>
                </tr>
                <tr>
                    <td align="left">6.registrationStatus</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>报名状态：00-已报名，10-已取消</td>
                </tr>
                <tr>
                    <td align="left">7.startTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>报名开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">8.endTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>报名结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "activityId": 1,
  "userName": "张三",
  "userPhone": "13800138000",
  "registrationStatus": "00",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">用户报名列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.activityId</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">活动ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.activityName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.userId</td>
                    <td colspan="2">String</td>
                    <td colspan="2">用户ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.userNickname</td>
                    <td colspan="2">String</td>
                    <td colspan="2">用户昵称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.7.registrationData</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名数据JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.8.registrationTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.9.status</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名状态</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityId": 1,
        "activityName": "春季摄影大赛",
        "categoryName": "摄影比赛",
        "userId": "user123",
        "userNickname": "张三",
        "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
        "registrationTime": "2024-02-15 14:30:00",
        "status": "10"
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">用户报名详情接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台查询用户报名详情信息，包含用户填写的表单数据。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">查询用户报名详情信息，包含用户填写的表单数据</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/userRegistration/detail</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>Y</td>
                    <td>报名记录主键ID</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">用户报名详情</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.activityId</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">活动ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.3.activityName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.4.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.5.userId</td>
                    <td colspan="2">String</td>
                    <td colspan="2">用户ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.6.userNickname</td>
                    <td colspan="2">String</td>
                    <td colspan="2">用户昵称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.7.registrationData</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名数据JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.8.registrationTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.9.status</td>
                    <td colspan="2">String</td>
                    <td colspan="2">报名状态</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "activityId": 1,
    "activityName": "春季摄影大赛",
    "categoryName": "摄影比赛",
    "userId": "user123",
    "userNickname": "张三",
    "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
    "registrationTime": "2024-02-15 14:30:00",
    "status": "10"
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <div style="margin-bottom:20px;">
        <h4 class="first_title">小程序端接口</h4>

        <div>
            <h4 class="second_title">分页查询活动列表接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>小程序端分页查询活动列表，支持按类别和状态筛选，只返回已发布的活动。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">小程序端分页查询活动列表，支持按类别和状态筛选，只返回已发布的活动</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-api</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">小程序前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-api/activity/page</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.categoryId</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别ID</td>
                </tr>
                <tr>
                    <td align="left">4.activityStatus</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动状态：10-待开始，20-进行中，40-已结束</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryId": 1,
  "activityStatus": "10"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.categoryId</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">活动类别ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.activityName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.activityDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.activityImageUrl</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动图片URL</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.7.startTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动开始时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.8.endTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动结束时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.9.activityStatus</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动状态</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.10.registrationCount</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">报名人数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.11.isRegistered</td>
                    <td colspan="2">Boolean</td>
                    <td colspan="2">当前用户是否已报名</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "activityName": "春季摄影大赛",
        "activityDesc": "欢迎参加春季摄影大赛",
        "activityImageUrl": "http://minio.example.com/activity1.jpg",
        "startTime": "2024-03-01 09:00:00",
        "endTime": "2024-03-31 18:00:00",
        "activityStatus": "20",
        "registrationCount": 15,
        "isRegistered": false
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">活动详情接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>小程序端查询活动详情信息，包含活动基本信息和报名表单配置。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">小程序端查询活动详情信息，包含活动基本信息和报名表单配置</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-api</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">小程序前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-api/activity/detail</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>Y</td>
                    <td>活动ID</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">活动详情</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.categoryId</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">活动类别ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.3.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.4.activityName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.5.activityDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.6.activityImageUrl</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动图片URL</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.7.startTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动开始时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.8.endTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动结束时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.9.activityStatus</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动状态</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.10.registrationCount</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">报名人数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.11.formConfig</td>
                    <td colspan="2">String</td>
                    <td colspan="2">表单配置JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.12.isRegistered</td>
                    <td colspan="2">Boolean</td>
                    <td colspan="2">当前用户是否已报名</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.13.registrationData</td>
                    <td colspan="2">String</td>
                    <td colspan="2">用户报名数据JSON（已报名时返回）</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryId": 1,
    "categoryName": "摄影比赛",
    "activityName": "春季摄影大赛",
    "activityDesc": "欢迎参加春季摄影大赛",
    "activityImageUrl": "http://minio.example.com/activity1.jpg",
    "startTime": "2024-03-01 09:00:00",
    "endTime": "2024-03-31 18:00:00",
    "activityStatus": "20",
    "registrationCount": 15,
    "formConfig": "[{\"type\":\"input\",\"label\":\"姓名\",\"required\":true}]",
    "isRegistered": false,
    "registrationData": null
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">活动类别列表接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>小程序端获取所有活动类别列表，用于筛选。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">小程序端获取所有活动类别列表，用于筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-api</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">小程序前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-api/activityCategory/list</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">用户报名接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>小程序端用户报名参加活动，提交报名表单数据。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">小程序端用户报名参加活动，提交报名表单数据</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-api</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">小程序前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-api/userRegistration/register</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.activityId</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动ID</td>
                </tr>
                <tr>
                    <td align="left">2.userId</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>用户ID（微信openid）</td>
                </tr>
                <tr>
                    <td align="left">3.formData</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>用户填写的表单数据JSON</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "activityId": 1,
  "userId": "oXYZ123456789",
  "formData": "{\"姓名\":\"张三\",\"手机号\":\"13800138000\",\"邮箱\":\"<EMAIL>\"}"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
        注：本文档为活动管理系统接口设计文档 - 用户报名管理模块及小程序端接口部分。<br>
        字体设置：正文宋体五号，标题宋体小四，完全符合Word文档标准。<br>
        表格格式已优化，复制到Word时将完美保持格式。
    </p>
</div>
</body>
</html>
