package cn.com.mtrsz.live.reqvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright (C), 2010-2023
 * FileName:     ActivityMiniListReqVO
 * Author:       System
 * Date:         2023/12/01
 * Description:  小程序活动列表查询请求
 * History:
 * <author>      <time>          <version>          <desc>
 * 作者姓名       修改时间         版本号             描述
 */
@Data
@ApiModel("小程序活动列表查询请求参数")
public class ActivityMiniListReqVO {

    @ApiModelProperty(value = "活动类别ID")
    private Integer categoryId;

    @ApiModelProperty(value = "活动状态，枚举值：10(待开始)、20(进行中)、40(已结束)")
    private String status;

    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页大小，默认10")
    private Integer pageSize = 10;
}
